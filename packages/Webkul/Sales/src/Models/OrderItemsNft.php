<?php

namespace Webkul\Sales\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Webkul\Sales\Contracts\OrderItem as OrderItemContract;

class OrderItemsNft extends Model implements OrderItemContract
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_items_nft';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'order_item_id',
        'order_id',
        'nft_id',
        'product_id',
        'customer_id',
    ];

    public function orderItems()
    {
        return $this->belongsTo(OrderItemProxy::modelClass());
    }
}
