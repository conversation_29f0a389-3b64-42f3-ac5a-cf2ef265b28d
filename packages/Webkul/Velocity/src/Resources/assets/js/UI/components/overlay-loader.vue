<template>
    <div class="modal-parent" v-if="$root.loading">
        <div class="overlay-loader">
            <div class="spinner">
                <div class="rect1"></div>
                <div class="rect2"></div>
                <div class="rect3"></div>
                <div class="rect4"></div>
                <div class="rect5"></div>
            </div>
        </div>
    </div>
</template>

<style scoped>
    .message-block {
        position: relative;
        top: 60px;
        color: #fff;
        font-size: 16px;
    }
</style>

<script>
    export default {};
</script>
