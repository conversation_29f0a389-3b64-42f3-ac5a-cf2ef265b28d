<?php

return [
    'admin' => [
        'catalog' => [
            'products' => [
                'booking'                   => 'Booking Information',
                'booking-type'              => 'Booking Type',
                'default'                   => 'Default',
                'appointment-booking'       => 'Appointment Booking',
                'event-booking'             => 'Event Booking',
                'rental-booking'            => 'Rental Booking',
                'table-booking'             => 'Table Booking',
                'slot-duration'             => 'Slot Duration (Mins)',
                'break-time'                => 'Break Time b/w Slots (Mins)',
                'available-every-week'      => 'Available Every Week',
                'yes'                       => 'Yes',
                'no'                        => 'No',
                'available-from'            => 'Available From',
                'available-to'              => 'Available To',
                'same-slot-all-days'        => 'Same Slot All Days',
                'slot-has-quantity'         => 'Slot has Quantity',
                'slots'                     => 'Slots',
                'from'                      => 'From',
                'to'                        => 'To',
                'qty'                       => 'Qty',
                'add-slot'                  => 'Add Slot',
                'sunday'                    => 'Sunday',
                'monday'                    => 'Monday',
                'tuesday'                   => 'Tuesday',
                'wednesday'                 => 'Wednesday',
                'thursday'                  => 'Thursday',
                'friday'                    => 'Friday',
                'saturday'                  => 'Saturday',
                'renting-type'              => 'Renting Type',
                'daily'                     => 'Daily Basis',
                'hourly'                    => 'Hourly Basis',
                'daily-hourly'              => 'Both (Daily and Hourly Basis)',
                'daily-price'               => 'Daily Price',
                'hourly-price'              => 'Hourly Price',
                'location'                  => 'Location',
                'show-location'             => 'Show Location',
                'event-start-date'          => 'Event Start Date',
                'event-end-date'            => 'Event End Date',
                'tickets'                   => 'Tickets',
                'add-ticket'                => 'Add Ticket',
                'name'                      => 'Name',
                'price'                     => 'Price',
                'quantity'                  => 'Quantity',
                'description'               => 'Description',
                'special-price'             => 'Special Price',
                'special-price-from'        => 'Valid From',
                'special-price-to'          => 'Valid Until',
                'charged-per'               => 'Charged Per',
                'guest'                     => 'Guest',
                'table'                     => 'Table',
                'prevent-scheduling-before' => 'Prevent Scheduling Before',
                'guest-limit'               => 'Guest Limit Per Table',
                'guest-capacity'            => 'Guest Capacity',
                'type'                      => 'Type',
                'many-bookings-for-one-day' => 'Many Bookings for One Day',
                'one-booking-for-many-days' => 'One Booking for Many Days',
                'day'                       => 'Day',
                'status'                    => 'Status',
                'open'                      => 'Open',
                'close'                     => 'Close',
                'time-error'                => 'The to time must be greater than the from time.',
            ],
        ],

        'sales' => [
            'bookings' => [
                'title' => 'Bookings',
            ],
        ],

        'datagrid' => [
            'from' => 'From',
            'to'   => 'To',
        ],
    ],

    'shop' => [
        'products' => [
            'booking-information'      => 'Booking Information',
            'location'                 => 'Location',
            'contact'                  => 'Contact',
            'email'                    => 'Email',
            'slot-duration'            => 'Slot Duration',
            'slot-duration-in-minutes' => ':minutes Minutes',
            'today-availability'       => 'Today Availability',
            'slots-for-all-days'       => 'Show for all days',
            'sunday'                   => 'Sunday',
            'monday'                   => 'Monday',
            'tuesday'                  => 'Tuesday',
            'wednesday'                => 'Wednesday',
            'thursday'                 => 'Thursday',
            'friday'                   => 'Friday',
            'saturday'                 => 'Saturday',
            'closed'                   => 'Closed',
            'book-an-appointment'      => 'Book an Appointment',
            'date'                     => 'Date',
            'slot'                     => 'Slot',
            'no-slots-available'       => 'No slots available',
            'rent-an-item'             => 'Rent an Item',
            'choose-rent-option'       => 'Choose Rent Option',
            'daily-basis'              => 'Daily Basis',
            'hourly-basis'             => 'Hourly Basis',
            'select-time-slot'         => 'Select time slot',
            'select-slot'              => 'Select Slot',
            'select-date'              => 'Select date',
            'select-rent-time'         => 'Select Rent Time',
            'from'                     => 'From',
            'to'                       => 'To',
            'book-a-table'             => 'Book a Table',
            'special-notes'            => 'Special Request/Notes',
            'event-on'                 => 'Event On',
            'book-your-ticket'         => 'Book Your Ticket',
            'per-ticket-price'         => ':price Per Ticket',
            'number-of-tickets'        => 'Number of Tickets',
            'total-tickets'            => 'Total Tickets',
            'base-price'               => 'Base Price',
            'total-price'              => 'Total Price',
            'base-price-info'          => '(This will be apply to each type of ticket for each quantity)',
        ],

        'cart' => [
            'renting_type' => 'Rent Type',
            'daily'        => 'Daily',
            'hourly'       => 'Hourly',
            'event-ticket' => 'Event Ticket',
            'event-from'   => 'Event From',
            'event-till'   => 'Event Till',
            'rent-type'    => 'Rent Type',
            'rent-from'    => 'Rent From',
            'rent-till'    => 'Rent Till',
            'booking-from' => 'Booking From',
            'booking-till' => 'Booking Till',
            'special-note' => 'Special Request/Notes',
        ],
    ],
];
