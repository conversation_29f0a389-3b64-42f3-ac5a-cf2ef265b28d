<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Thorne\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Traits\LogsMiddleware;
use Thorne\SecurityLayer\Traits\ResolvesModule;

class TimestampMiddleware
{
    use LogsMiddleware, ResolvesModule;

    protected SecurityLayerExceptionHandler $handler;

    public function __construct(SecurityLayerExceptionHandler $handler)
    {
        $this->handler = $handler;
    }

    public function handle(Request $request, Closure $next)
    {
        $moduleKey = $this->resolveModuleKey($request, 'timestamp');
        $config    = $this->resolveModuleConfig($moduleKey, 'timestamp');

        if (! ($config['enabled'] ?? false)) {
            return $next($request);
        }

        $timestamp = $request->header($config['header_name']);
        if (! $timestamp) {
            $this->logWarning('timestamp', 'Missing', [
                'moduleKey' => $moduleKey,
                'header'    => $config['header_name'],
            ]);

            return $this->handler->handle($request, 'timestamp', 'missing');
        }

        $now     = time();
        $latency = abs($now - $timestamp);

        $this->logInfo('timestamp', 'Checked: "timestamp" middleware', [
            'moduleKey' => $moduleKey,
            'timestamp' => $timestamp,
            'now'       => $now,
            'latency'   => $latency,
        ]);

        if ($latency > ($config['tolerance'])) {
            $this->logWarning('timestamp', 'Invalid: "timestamp" middleware', [
                'moduleKey' => $moduleKey,
                'timestamp' => $timestamp,
                'now'       => $now,
                'latency'   => $latency,
                'tolerance' => $config['tolerance'],
            ]);

            return $this->handler->handle($request, 'timestamp', 'invalid');
        }

        return $next($request);
    }
}
