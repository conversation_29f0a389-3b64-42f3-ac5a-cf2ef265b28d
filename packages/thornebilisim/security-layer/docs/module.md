# Mod<PERSON>ler Yapı

`thornebilisim/security-layer` paketinin en güçlü özelliklerinden biri, **mod<PERSON>ler mimarisi**dir. <PERSON><PERSON> yap<PERSON>, uygulamanızın farklı b<PERSON><PERSON><PERSON><PERSON><PERSON>, mikroservisleri veya API versiyonları için tamamen özelleştirilmiş ve bağımsız güvenlik politikaları tanımlamanıza olanak tanır. Genel güvenlik ayarlarınızdan ayrışarak, her modülün kendi özel gereksinimlerini karşılamasını sağlayabilirsiniz.

## Modüllerin Amacı

Büyük ve karmaşık API'ler veya mikroservis mimarileri genellikle farklı uç noktalar için farklı güvenlik gereksinimlerine sahiptir:

*   Bir yönetici paneli API'si, halka açık bir müşteri API'sinden daha katı hız sınırlamalarına veya farklı CORS politikalarına ihtiyaç duyabilir.
*   <PERSON>z<PERSON> hizmetler belirli imza algoritmaları veya anahtarları kullanırken, diğerleri kullanmayabilir.
*   Farklı API versiyonları (örn. `v1`, `v2`) güvenlik protokollerinde değişiklik gösterebilir.

Modüler yapı, bu tür senaryoları merkezi `config/security-layer.php` dosyası içinde yönetmenize olanak tanır, böylece kodunuzu temiz ve düzenli tutarsınız.

## Modül Tanımlama

Modüller, `config/security-layer.php` dosyasındaki `modules` dizisi içinde tanımlanır:

```php
// config/security-layer.php

return [
    // ... diğer ayarlar ...

    'modules' => [
        'test-module' => [ // Modül anahtarı (örn. 'admin-panel', 'v2-api')
            'enabled'     => true, // Bu modülün etkin olup olmadığını belirler
            'features'    => [
                // Bu modüle özel güvenlik özelliklerinin yapılandırması
                'cors' => [
                    'enabled'                 => env('SECURITY_LAYER_TEST_MODULE_CORS_ENABLED', true),
                    'allowed_origins'         => env('SECURITY_LAYER_TEST_MODULE_CORS_ALLOWED_ORIGINS') ? array_filter(explode(',', env('SECURITY_LAYER_TEST_MODULE_CORS_ALLOWED_ORIGINS'))) : ['https://test-module.example.com'],
                    'default_allowed_methods' => env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_METHODS') ? array_filter(explode(',', env('SECURITY_LAYER_TEST_MODULE_CORS_DEFAULT_ALLOWED_METHODS'))) : ['GET', 'POST'],
                    // ... diğer CORS ayarları ...
                ],
                'rate_limit' => [
                    'enabled'        => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_ENABLED', true),
                    'global'         => [
                        'limit'  => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_GLOBAL_LIMIT', 5),
                        'window' => env('SECURITY_LAYER_TEST_MODULE_RATE_LIMIT_GLOBAL_WINDOW', 1),
                    ],
                    // ... diğer Rate Limit ayarları ...
                ],
                // ... diğer özellikler (nonce, timestamp, signature, idempotency, session, trace)
                // Her bir özelliğin kendi 'enabled' bayrağı olabilir
            ],
        ],
        'another-module' => [
            'enabled' => false, // Bu modülü devre dışı bırak
            'features' => [
                // ...
            ]
        ]
    ],
];
```

### Modül Yapılandırma Detayları

*   **Modül Anahtarı**: `test-module` gibi, modülü benzersiz şekilde tanımlayan bir anahtar. Bu anahtar, rotalarınızda middleware'i çağırırken kullanılacaktır.
*   **`enabled`**: Bu bayrak, tüm modülün etkin olup olmadığını kontrol eder. Eğer bir modül devre dışı bırakılırsa, o modüle atanan tüm middleware'ler genel (global) yapılandırmaya geri döner veya devre dışı kalır (eğer globalde de etkin değillerse).
*   **`features`**: Bu dizi, modüle özel olarak geçersiz kılınan güvenlik özelliklerini içerir. Global `features` bölümündeki yapılandırmayla aynı yapıyı izler. Örneğin, `test-module` altındaki `cors` yapılandırması, genel `features.cors` yapılandırmasını geçersiz kılacaktır.

## Modül Bazlı Middleware Kullanımı

Bir rotaya modüle özel bir middleware uygulamak için, middleware takma adının önüne modül anahtarını eklersiniz:

`security-layer.{modül-anahtarı}.{middleware-takma-adı}`

**Örnek:**

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::prefix('admin')->group(function () {
    // 'admin-panel' modülünün CORS ve Rate Limit ayarlarını kullanır
    Route::get('/dashboard', function () {
        return response()->json(['message' => 'Admin Paneli Verileri']);
    })->middleware([
        'security-layer.admin-panel.cors',
        'security-layer.admin-panel.rate_limit',
    ]);

    // 'admin-panel' modülünün signature ayarını kullanır
    Route::post('/settings', function () {
        return response()->json(['message' => 'Admin ayarları güncellendi']);
    })->middleware([
        'security-layer.admin-panel.signature',
        'security-layer.admin-panel.timestamp',
        'security-layer.admin-panel.nonce',
    ]);
});
```

### Modül ve Global Ayarların Birleşimi

Bir modül, yalnızca yapılandırma dosyasında (veya ortam değişkenlerinde) açıkça tanımladığı ayarları geçersiz kılar. Bir özellik için modül içinde herhangi bir ayar tanımlanmamışsa, ilgili global ayarlar kullanılmaya devam eder.

**Örnek Senaryo:**

Diyelim ki global `cors` ayarınızda `default_max_age` 86400 (24 saat) olarak ayarlandı.
Eğer `test-module` içindeki `cors` özelliğinde `default_max_age` tanımlamazsanız, `security-layer.test-module.cors` middleware'ı yine de globaldeki 86400 değerini kullanacaktır. Ancak `allowed_origins` gibi modül içinde tanımlanmış diğer CORS ayarları modüle özel olacaktır.

Bu davranış, `Thorne\SecurityLayer\Traits\ResolvesModule` trait'i tarafından yönetilir, bu trait, doğru yapılandırma setini (global, modül veya inline) dinamik olarak belirler.

## Modüler Yapının Avantajları

*   **Esneklik**: Farklı API uç noktaları için benzersiz güvenlik gereksinimlerini kolayca karşılayın.
*   **Organizasyon**: Güvenlik politikalarını mantıksal modüller halinde düzenleyerek büyük projelerde yönetimi kolaylaştırın.
*   **Bakım Kolaylığı**: Bir modülün güvenlik politikasındaki değişiklikler, uygulamanın diğer bağımsız bölümlerini etkilemez.
*   **Yeniden Kullanılabilirlik**: Ortak güvenlik politika setlerini modül olarak tanımlayabilir ve birden fazla yerde kullanabilirsiniz.

Modüler yapı, API'lerinizin büyüklüğü ve karmaşıklığı ne olursa olsun, `security-layer` paketini projenize sorunsuz bir şekilde entegre etmenize olanak tanıyan güçlü bir özelliktir.