# <PERSON><PERSON><PERSON><PERSON><PERSON>kleme

`thornebilisim/security-layer` p<PERSON><PERSON>, API güvenliği ve istek yönetimi olaylarını detaylı bir şekilde kaydetmek için kapsamlı bir günlükleme mekanizması sunar. <PERSON><PERSON> gü<PERSON><PERSON><PERSON><PERSON><PERSON>, güvenlik olaylarını izlemek, potansiyel tehditleri tespit etmek, performans sorunlarını gidermek ve denetim kayıtları tutmak için hayati öneme sahiptir.

## Günlükleme Kanalı

Paket, Laravel'in `config/logging.php` dosyasında özel bir günlükleme kanalı tanımlar. Bu kanal varsayılan olarak `security-layer` adını taşır ve tüm güvenlik katmanı olaylarını bu kanal üzerinden günlüğe kaydeder.

Varsayılan `config/logging.php` yapılandırması:

```php
<?php

return [
    'security-layer' => [
        'driver' => 'daily',
        'path'   => storage_path('logs/security-layer.log'),
        'level'  => env('SECURITY_LAYER_LOG_LEVEL', 'info'),
        'days'   => 30,
        'tap'    => [Thorne\SecurityLayer\Logging\SecurityLayerLogJson::class],
    ],
];
```

*   **`driver: 'daily'`**: Günlüklerin her gün yeni bir dosyaya yazılmasını sağlar. Bu, günlük dosyalarının boyutunu yönetmek ve geçmişe dönük verilere erişimi kolaylaştırmak için kullanışlıdır.
*   **`path: storage_path('logs/security-layer.log')`**: Günlük dosyalarının varsayılan olarak depolandığı konumdur. Bu dizin içinde `security-layer-YYYY-MM-DD.log` formatında dosyalar bulacaksınız.
*   **`level: env('SECURITY_LAYER_LOG_LEVEL', 'info')`**: Günlüğe kaydedilecek minimum olay seviyesini belirler. Bu değeri `.env` dosyanızda `SECURITY_LAYER_LOG_LEVEL` ortam değişkeni ile değiştirebilirsiniz. Örneğin, sadece kritik hataları görmek için `error` seviyesini ayarlayabilirsiniz. Kabul edilen seviyeler: `debug`, `info`, `notice`, `warning`, `error`, `critical`, `alert`, `emergency`.
*   **`days: 30`**: `daily` sürücüsü kullanılıyorsa, kaç günlük geçmiş günlük dosyasının saklanacağını belirler. Daha eski dosyalar otomatik olarak silinir.
*   **`tap: [Thorne\SecurityLayer\Logging\SecurityLayerLogJson::class]`**: Bu kısım, paketin günlükleme çıktısını özelleştirmek için özel bir "tapper" sınıfı kullanır.

## Günlük Formatı: JSON ve Okunabilirlik

`Thorne\SecurityLayer\Logging\SecurityLayerLogJson` tapper sınıfı sayesinde, `security-layer` kanalı üzerinden yazılan tüm günlükler JSON formatında yapılandırılmıştır. Bu, günlük verilerinin programatik olarak kolayca ayrıştırılmasını ve dış sistemlere (örneğin ELK Stack, Splunk, Datadog) aktarılmasını sağlar.

Ayrıca, insan okunabilirliğini artırmak için her JSON kaydının başına otomatik olarak bir ön ek eklenir. Bu ön ek, olayın zaman damgasını, uygulama ortamını ve günlük seviyesini içerir.

**Örnek Günlük Kaydı (Kısmi)**:

```
[2023-10-27 10:30:00] local.INFO: [cors] Checked: "cors" middleware{"moduleKey":"global","origin":"https:\/\/your-frontend.com","origins":["https:\/\/api.example.com","https:\/\/your-frontend.com"]}
```

Yukarıdaki örnekte:
*   `[2023-10-27 10:30:00] local.INFO:` kısmı insan okunabilir ön ektir.
*   `[cors]` günlükleyici kanalının (middleware adının) bir parçasıdır.
*   `Checked: "cors" middleware` günlük mesajıdır.
*   `{"moduleKey":"global","origin":"https:\/\/your-frontend.com","origins":["https:\/\/api.example.com","https:\/\/your-frontend.com"]}` kısmı ise olayın detaylarını içeren JSON verisidir.

Bu yapılandırılmış günlükler, aşağıdaki gibi bilgileri içerebilir (özelliğe ve olaya bağlı olarak):

*   Middleware adı (örn. `https`, `cors`, `nonce`, `signature`, `trace`)
*   Modül anahtarı (`moduleKey`)
*   İstek URL'si, metodu, IP adresi, kullanıcı aracısı
*   İstek başlıkları ve içeriği (hassas veriler maskelenmiş olarak)
*   Yanıt durumu ve başlıkları
*   Doğrulama sonuçları (başarılı/başarısız)
*   Hata kodları ve mesajları
*   İstek süresi, bellek kullanımı (Trace middleware için)

## Günlükleme Kontrolü

Günlükleme davranışını `config/security-layer.php` dosyası veya `.env` ortam değişkenleri aracılığıyla kontrol edebilirsiniz:

*   **`SECURITY_LAYER_LOG_ENABLED`**: Günlüklemeyi tamamen açıp kapatmak için kullanılır. `false` olarak ayarlanırsa, hiçbir güvenlik katmanı günlüğü yazılmaz.
*   **`SECURITY_LAYER_LOG_LEVEL`**: Sadece belirli bir önem seviyesinin üzerindeki günlüklerin kaydedilmesini sağlamak için kullanılır. Bu, üretim ortamlarında günlük gürültüsünü azaltmaya yardımcı olur.

## Güvenlik ve İzleme İçin Günlüklerin Önemi

`security-layer` günlükleri, API güvenliğiniz için kritik bir denetim izi sağlar:

*   **Saldırı Tespiti**: Replay saldırıları, hız sınırlama ihlalleri, imza doğrulama başarısızlıkları gibi olaylar anında günlüğe kaydedilir ve potansiyel saldırı girişimlerini tespit etmenize yardımcı olur.
*   **Hata Ayıklama**: Middleware zincirindeki akışı ve doğrulama sonuçlarını izleyerek API isteklerindeki sorunları hızlıca teşhis edebilirsiniz.
*   **Uyumluluk**: Bazı düzenlemeler (örneğin GDPR, HIPAA) erişim ve veri işleme faaliyetlerinin kayıt altına alınmasını gerektirebilir. Detaylı günlükler bu gereksinimleri karşılamanıza yardımcı olur.
*   **Performans Analizi**: `TraceMiddleware` tarafından kaydedilen süre ve bellek kullanımı gibi metrikler, API performans darboğazlarını belirlemenize yardımcı olabilir.
*   **Hassas Veri Güvenliği**: Günlüklerdeki hassas verilerin (parolalar, API anahtarları vb.) otomatik olarak maskelenmesi, güvenlik ihlali riskini azaltır.

Günlükleri düzenli olarak incelemeniz ve izleme araçlarıyla entegre etmeniz, API'lerinizin güvenliğini ve istikrarını proaktif bir şekilde sağlamanıza olanak tanır.
