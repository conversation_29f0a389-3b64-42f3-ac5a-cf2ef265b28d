<?php

namespace Thorne\FastOrder\Providers;

use Illuminate\Support\ServiceProvider;
use Thorne\FastOrder\Payment\FastOrderPayment;

class FastOrderServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(FastOrderPayment::class, function () {
            return new FastOrderPayment;
        });

        $this->registerConfigs();

        $path = realpath(__DIR__.'/../');
        $this->mergeConfigFrom(
            $path.'/../config/system.php', 'core',
        );
        $this->mergeConfigFrom(
            $path.'/../config/paymentmethods.php', 'paymentmethods',
        );
    }

    public function boot(): void
    {
        $path = realpath(__DIR__.'/../');
        $this->loadRoutesFrom($path.'/../routes/GlobalRouters.php');
        $this->loadMigrationsFrom($path.'/../database/migrations');
        $this->loadViewsFrom($path.'/../resources/views', 'fast-order');
        $this->loadTranslationsFrom($path.'/../resources/lang', 'fast-order');

        $this->publishes([
            $path.'/../config/system.php'          => config_path('fast-order-system.php'),
            $path.'/../config/paymentmethods.php'  => config_path('fast-order-methods.php'),
            $path.'/../config/fast-order.php'      => config_path('fast-order.php'),
        ]);

        if ($this->app->runningInConsole()) {
            $this->commands([]);
        }
    }

    protected function registerConfigs()
    {
        $this->mergeConfigFrom(
            dirname(__DIR__).'/../config/logging.php', 'logging.channels'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__).'/../config/queue.php', 'queue.connections'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__).'/../config/fast-order.php', 'fast-order'
        );
    }
}
