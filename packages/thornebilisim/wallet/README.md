# Thorne Bilişim Wallet Package

Bu paket, <PERSON>'in projesinde müşteri cüzdan fonksiyonelliğini sağlayan kapsamlı bir wallet yönetim sistemidir.

## Özellikler

### 🏦 Çok Para Birimli Destek
- **EUR** (Euro) - Varsayılan para birimi
- **USD** (US Dollar) 
- **MLGR** (Miligram) - Özel sistem para birimi

### 🔢 Luhn Algoritmalı Hesap Numaraları
- 11 haneli benzersiz hesap numaraları
- Format: `[Para Birimi ISO Kodu (3)] + [<PERSON><PERSON><PERSON> (1)] + [<PERSON><PERSON><PERSON><PERSON> (7)]`
- <PERSON>hn algoritması ile doğrulama
- Örnek: `978-4-1234567` (EUR hesabı)

### 💰 Temel Wallet İşlemleri

#### 1. Bakiye Yükleme (Deposit)
- Çeşitli ödeme yöntemleri (Banka Havalesi, Qonto, PayPal)
- Şirket hesap bilgileri entegrasyonu
- Otomatik işlem takibi

#### 2. Para Çekme (Withdraw)
- Esnek ödeme yöntemi desteği
- Bakiye kontrolü ve doğrulama
- İşlem onay mekanizması

#### 3. Hesaplar Arası Transfer
- Sistem içi müşteriler arası transfer
- Hesap numarası doğrulama
- Anlık transfer işlemleri

### 🔗 WEB3 Entegrasyonu
- Gerçek zamanlı bakiye senkronizasyonu
- Veritabanı ve WEB3 bakiye karşılaştırması
- Otomatik bakiye güncelleme
- Cache mekanizması

### 🛡️ Güvenlik ve Yetkilendirme
- İşlem bazlı izin kontrolü
- Günlük/aylık limit yönetimi
- KYC doğrulama entegrasyonu
- Para birimi bazlı kısıtlamalar

## Kurulum

### 1. Composer ile Kurulum
```bash
composer require thornebilisim/wallet
```

### 2. Konfigürasyon Dosyalarını Yayınlama
```bash
php artisan vendor:publish --tag=wallet-config
```

### 3. Veritabanı Migrasyonları
```bash
php artisan migrate
```

### 4. Ortam Değişkenleri (.env)
```env
# Wallet Genel Ayarları
WALLET_ENABLED=true

# WEB3 Entegrasyonu
WALLET_WEB3_ENABLED=true
WALLET_WEB3_BASE_URL=https://api.example.com
WALLET_WEB3_TENANT_ID=50
WALLET_WEB3_CHAIN_ID=mirum-testnet
WALLET_WEB3_CACHE_TTL=1800

# Ödeme Yöntemleri
WALLET_BANK_TRANSFER_ENABLED=true
WALLET_QONTO_ENABLED=true
WALLET_PAYPAL_ENABLED=false

# İzinler ve Limitler
WALLET_DEPOSIT_REQUIRE_KYC=true
WALLET_DEPOSIT_DAILY_LIMIT=10000.00
WALLET_WITHDRAW_REQUIRE_KYC=true
WALLET_WITHDRAW_DAILY_LIMIT=5000.00
WALLET_TRANSFER_DAILY_LIMIT=2000.00

# Loglama
WALLET_LOGGING_ENABLED=true
WALLET_LOG_CHANNEL=wallet
```

## Kullanım

### Wallet Servisini Kullanma

```php
use Thorne\Wallet\Services\WalletService;

// Müşteri için wallet başlatma
$walletService = app(WalletService::class);
$customer = auth()->guard('customer')->user();

// Wallet genel bakış
$overview = $walletService->getWalletOverview($customer, true); // true = WEB3 sync

// Deposit oluşturma
$deposit = $walletService->createDeposit([
    'customer_id' => $customer->id,
    'method' => 'bank_transfer',
    'currency' => 'EUR',
    'amount' => '100.00',
    'description' => 'Test deposit',
]);

// Withdrawal oluşturma
$withdrawal = $walletService->createWithdrawal([
    'customer_id' => $customer->id,
    'method' => 'bank_transfer',
    'currency' => 'EUR',
    'amount' => '50.00',
    'description' => 'Test withdrawal',
]);
```

### Luhn Servisini Kullanma

```php
use Thorne\Wallet\Services\LuhnService;

$luhnService = app(LuhnService::class);

// Hesap numarası oluşturma
$accountNumber = $luhnService->generateAccountNumber('978'); // EUR için
// Sonuç: *********** (11 haneli)

// Hesap numarası doğrulama
$isValid = $luhnService->validateAccountNumber('***********');

// Para birimi çıkarma
$currency = $luhnService->extractCurrencyCode('***********'); // "978"
```

### Transfer İşlemleri

```php
use Thorne\Wallet\Services\TransferService;

$transferService = app(TransferService::class);

// Transfer oluşturma
$transfer = $transferService->createTransfer([
    'from_account_number' => '***********',
    'to_account_number' => '***********',
    'amount' => '25.00',
    'description' => 'Test transfer',
]);

// Transfer işleme
$transferService->processTransfer($transfer);
```

### Bakiye Yönetimi

```php
use Thorne\Wallet\Services\BalanceService;

$balanceService = app(BalanceService::class);

// Bakiye sorgulama
$balance = $balanceService->getBalance($customerId, 'EUR');

// WEB3 ile senkronizasyon
$syncedBalance = $balanceService->syncWithWeb3($customerId, 'EUR');

// Bakiye karşılaştırma
$comparison = $balanceService->compareBalances($customerId, 'EUR');
```

## API Endpoints

### Genel Wallet İşlemleri
- `GET /api/wallet/overview` - Wallet genel bakış
- `GET /api/wallet/balance` - Bakiye sorgulama
- `POST /api/wallet/sync-balance` - Bakiye senkronizasyonu
- `GET /api/wallet/accounts` - Hesap listesi
- `POST /api/wallet/accounts` - Yeni hesap oluşturma

### Deposit İşlemleri
- `GET /api/wallet/deposits` - Deposit listesi
- `POST /api/wallet/deposits` - Yeni deposit
- `GET /api/wallet/deposits/methods` - Deposit yöntemleri
- `GET /api/wallet/deposits/company-accounts` - Şirket hesapları

### Withdrawal İşlemleri
- `GET /api/wallet/withdrawals` - Withdrawal listesi
- `POST /api/wallet/withdrawals` - Yeni withdrawal
- `GET /api/wallet/withdrawals/methods` - Withdrawal yöntemleri

### Transfer İşlemleri
- `GET /api/wallet/transfers` - Transfer listesi
- `POST /api/wallet/transfers` - Yeni transfer
- `GET /api/wallet/transfers/validate-account` - Hesap doğrulama
- `POST /api/wallet/transfers/{id}/cancel` - Transfer iptal

## Web Routes

### Müşteri Panel Rotaları
- `/customer/account/wallet` - Ana wallet sayfası
- `/customer/account/wallet/deposit` - Deposit işlemleri
- `/customer/account/wallet/withdraw` - Withdrawal işlemleri
- `/customer/account/wallet/transfer` - Transfer işlemleri
- `/customer/account/wallet/transactions` - İşlem geçmişi

## Veritabanı Yapısı

### Tablolar

#### `wallet_accounts`
- Müşteri hesap numaraları
- Para birimi bazlı hesaplar
- Luhn algoritmalı 11 haneli numaralar

#### `wallet_balances`
- Müşteri bakiyeleri (salt okunur)
- WEB3 senkronizasyon verileri
- Kilitli bakiye yönetimi

#### `wallet_transactions`
- Tüm wallet işlemleri
- Deposit, withdrawal, transfer kayıtları
- Durum ve işlem takibi

#### `wallet_transfers`
- Hesaplar arası transfer kayıtları
- Gönderen ve alıcı bilgileri
- Transfer durumu yönetimi

## Konfigürasyon

### Para Birimleri
```php
'currencies' => [
    'EUR' => [
        'iso_code' => '978',
        'name' => 'Euro',
        'symbol' => '€',
        'is_default' => true,
        'precision' => 2,
        'enabled' => true,
    ],
    // ...
],
```

### Ödeme Yöntemleri
```php
'payment_methods' => [
    'bank_transfer' => [
        'name' => 'Bank Transfer',
        'enabled' => true,
        'can_deposit' => true,
        'can_withdraw' => true,
        'supported_currencies' => ['EUR', 'USD'],
        'config' => [
            'min_amount' => 10.00,
            'max_amount' => 50000.00,
        ],
    ],
    // ...
],
```

### İzinler
```php
'permissions' => [
    'deposit' => [
        'enabled' => true,
        'require_kyc' => true,
        'daily_limit' => 10000.00,
        'monthly_limit' => 50000.00,
    ],
    // ...
],
```

## Güvenlik

- Tüm finansal işlemler transaction içinde gerçekleşir
- Luhn algoritması ile hesap numarası doğrulama
- WEB3 bakiye karşılaştırması
- İzin bazlı işlem kontrolü
- Detaylı loglama ve audit trail

## Geliştirme

### Test Etme
```bash
# Unit testler
php artisan test packages/thornebilisim/wallet/tests

# Luhn algoritması test
$luhnService = app(LuhnService::class);
$accountNumber = $luhnService->generateAccountNumber('978');
$isValid = $luhnService->validateAccountNumber($accountNumber);
```

### Loglama
```php
// Wallet işlemleri otomatik loglanır
Log::channel('wallet')->info('Transaction created', $data);
```

## Lisans

MIT License

## Destek

Teknik destek için: <EMAIL>
