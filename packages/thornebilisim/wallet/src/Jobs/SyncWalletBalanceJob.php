<?php

namespace Thorne\Wallet\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Services\BalanceService;

class SyncWalletBalanceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $backoff = 60; // seconds

    public function __construct(
        public int $customerId,
        public string $currency
    ) {}

    public function handle(BalanceService $balanceService): void
    {
        try {
            $balanceService->syncWithWeb3($this->customerId, $this->currency);
            
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->info('Balance sync job completed', [
                    'customer_id' => $this->customerId,
                    'currency' => $this->currency,
                ]);
                
        } catch (\Exception $e) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Balance sync job failed', [
                    'customer_id' => $this->customerId,
                    'currency' => $this->currency,
                    'error' => $e->getMessage(),
                ]);
                
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->error('Balance sync job failed permanently', [
                'customer_id' => $this->customerId,
                'currency' => $this->currency,
                'error' => $exception->getMessage(),
            ]);
    }
}
