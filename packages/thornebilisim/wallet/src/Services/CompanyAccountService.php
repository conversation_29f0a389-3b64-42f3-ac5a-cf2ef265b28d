<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class CompanyAccountService
{
    /**
     * Get all company accounts.
     */
    public function getAllAccounts(): array
    {
        return wallet_config('company_accounts', []);
    }

    /**
     * Get accounts for specific method.
     */
    public function getAccountsByMethod(string $method): array
    {
        $accounts = $this->getAllAccounts();
        return $accounts[$method] ?? [];
    }

    /**
     * Get accounts for specific method and currency.
     */
    public function getAccountsByMethodAndCurrency(string $method, string $currency): array
    {
        $methodAccounts = $this->getAccountsByMethod($method);
        $accounts = $methodAccounts[$currency] ?? [];

        return array_filter($accounts, fn($account) => $account['is_active']);
    }

    /**
     * Get account by ID.
     */
    public function getAccountById(string $accountId): ?array
    {
        $allAccounts = $this->getAllAccounts();

        foreach ($allAccounts as $method => $methodAccounts) {
            foreach ($methodAccounts as $currency => $accounts) {
                foreach ($accounts as $account) {
                    if ($account['id'] === $accountId && $account['is_active']) {
                        return $account;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Get primary account for method and currency.
     */
    public function getPrimaryAccount(string $method, string $currency): ?array
    {
        $accounts = $this->getAccountsByMethodAndCurrency($method, $currency);

        // Find primary account
        foreach ($accounts as $account) {
            if ($account['is_primary'] && $account['is_active']) {
                return $account;
            }
        }

        // If no primary found, return first active account
        return $accounts[0] ?? null;
    }

    /**
     * Get all active accounts for method.
     */
    public function getActiveAccountsByMethod(string $method): array
    {
        $methodAccounts = $this->getAccountsByMethod($method);
        $result = [];

        foreach ($methodAccounts as $currency => $accounts) {
            $activeAccounts = array_filter($accounts, fn($account) => $account['is_active']);
            $result = array_merge($result, $activeAccounts);
        }

        return $result;
    }

    /**
     * Get accounts grouped by currency for method.
     */
    public function getAccountsGroupedByCurrency(string $method): array
    {
        $methodAccounts = $this->getAccountsByMethod($method);
        $result = [];

        foreach ($methodAccounts as $currency => $accounts) {
            $activeAccounts = array_filter($accounts, fn($account) => $account['is_active']);
            if (!empty($activeAccounts)) {
                $result[$currency] = $activeAccounts;
            }
        }

        return $result;
    }

    /**
     * Get supported currencies for method.
     */
    public function getSupportedCurrencies(string $method): array
    {
        $methodAccounts = $this->getAccountsByMethod($method);
        $currencies = [];

        foreach ($methodAccounts as $currency => $accounts) {
            $hasActiveAccount = collect($accounts)->contains('is_active', true);
            if ($hasActiveAccount) {
                $currencies[] = $currency;
            }
        }

        return $currencies;
    }

    /**
     * Get supported methods.
     */
    public function getSupportedMethods(): array
    {
        return array_keys($this->getAllAccounts());
    }

    /**
     * Check if method and currency combination is supported.
     */
    public function isSupported(string $method, string $currency): bool
    {
        $accounts = $this->getAccountsByMethodAndCurrency($method, $currency);
        return !empty($accounts);
    }

    /**
     * Get account statistics.
     */
    public function getAccountStatistics(): array
    {
        $allAccounts = $this->getAllAccounts();
        $stats = [
            'total_methods' => 0,
            'total_currencies' => 0,
            'total_accounts' => 0,
            'active_accounts' => 0,
            'primary_accounts' => 0,
            'methods' => [],
        ];

        foreach ($allAccounts as $method => $methodAccounts) {
            $stats['total_methods']++;
            $methodStats = [
                'currencies' => 0,
                'accounts' => 0,
                'active_accounts' => 0,
                'primary_accounts' => 0,
            ];

            foreach ($methodAccounts as $currency => $accounts) {
                $methodStats['currencies']++;

                foreach ($accounts as $account) {
                    $stats['total_accounts']++;
                    $methodStats['accounts']++;

                    if ($account['is_active']) {
                        $stats['active_accounts']++;
                        $methodStats['active_accounts']++;
                    }

                    if ($account['is_primary']) {
                        $stats['primary_accounts']++;
                        $methodStats['primary_accounts']++;
                    }
                }
            }

            $stats['methods'][$method] = $methodStats;
        }

        $stats['total_currencies'] = collect($allAccounts)
            ->flatMap(fn($methodAccounts) => array_keys($methodAccounts))
            ->unique()
            ->count();

        return $stats;
    }

    /**
     * Validate account configuration.
     */
    public function validateAccountConfiguration(array $account): array
    {
        $errors = [];
        $requiredFields = ['id', 'name', 'currency', 'is_active', 'is_primary'];

        foreach ($requiredFields as $field) {
            if (!isset($account[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        if (isset($account['id']) && empty($account['id'])) {
            $errors[] = "Account ID cannot be empty";
        }

        if (isset($account['currency']) && !in_array($account['currency'], ['EUR', 'USD', 'MLGR'])) {
            $errors[] = "Unsupported currency: {$account['currency']}";
        }

        return $errors;
    }

    /**
     * Get account selection strategy for deposits.
     */
    public function getAccountForDeposit(string $method, string $currency, ?float $amount = null): ?array
    {
        $accounts = $this->getAccountsByMethodAndCurrency($method, $currency);

        if (empty($accounts)) {
            return null;
        }

        // Strategy 1: Use primary account if available
        $primaryAccount = $this->getPrimaryAccount($method, $currency);
        if ($primaryAccount) {
            return $primaryAccount;
        }

        // Strategy 2: Use first active account
        return $accounts[0] ?? null;
    }

    /**
     * Log account usage for analytics.
     */
    public function logAccountUsage(string $accountId, string $action, array $context = []): void
    {
        if (!wallet_config('logging.enabled', true)) {
            return;
        }

        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->info('Company account usage', [
                'account_id' => $accountId,
                'action' => $action,
                'context' => $context,
                'timestamp' => now()->toISOString(),
            ]);
    }
}
