<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Facades\DB;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletBalance;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Enums\TransactionStatus;
use Webkul\Customer\Models\Customer;

class WalletService
{
    public function __construct(
        protected LuhnService $luhnService,
        protected BalanceService $balanceService,
        protected TransferService $transferService
    ) {}

    /**
     * Initialize wallet for a customer.
     *
     * @param  Customer  $customer
     * @param  array  $currencies
     * @return array
     */
    public function initializeWallet(Customer $customer, array $currencies = null): array
    {
        $currencies = $currencies ?? array_keys(wallet_get_enabled_currencies());
        $accounts = [];

        DB::beginTransaction();

        try {
            foreach ($currencies as $currency) {
                if (! wallet_is_supported_currency($currency)) {
                    continue;
                }

                // Create account
                $account = WalletAccount::getOrCreateForCustomer($customer->id, $currency);

                // Create balance record
                $balance = WalletBalance::getOrCreateForCustomer($customer->id, $currency);

                $accounts[$currency] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
            }

            DB::commit();

            return $accounts;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get customer wallet summary.
     *
     * @param  Customer  $customer
     * @param  bool  $syncWithWeb3
     * @return array
     */
    public function getWalletSummary(Customer $customer, bool $syncWithWeb3 = false): array
    {
        $accounts = $customer->walletAccounts()->active()->with('balance')->get();
        $summary = [
            'customer_id' => $customer->id,
            'total_accounts' => $accounts->count(),
            'currencies' => [],
            'total_balance_usd' => '0.********', // Could be calculated with exchange rates
        ];

        foreach ($accounts as $account) {
            $balance = $account->balance;

            if ($syncWithWeb3 && $balance && $balance->needsWeb3Sync()) {
                $balance = $this->balanceService->syncWithWeb3($customer->id, $account->currency);
            }

            $summary['currencies'][$account->currency] = [
                'account_number' => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'balance' => $balance ? $balance->balance : '0.********',
                'locked_balance' => $balance ? $balance->locked_balance : '0.********',
                'available_balance' => $balance ? $balance->getAvailableBalance() : '0.********',
                'formatted_balance' => $balance ? $balance->getFormattedBalance() : '0.00',
                'formatted_available_balance' => $balance ? $balance->getFormattedAvailableBalance() : '0.00',
                'last_sync' => $balance?->last_web3_sync?->toISOString(),
                'is_active' => $account->is_active,
            ];
        }

        return $summary;
    }

    /**
     * Get all customer balances with optional WEB3 sync.
     */
    public function getCustomerBalances(int $customerId, bool $forceSync = false): array
    {
        if ($forceSync) {
            $balances = $this->balanceService->syncAllWithWeb3($customerId);
        } else {
            $balances = $this->balanceService->getAllBalances($customerId);
        }

        $result = [];

        foreach ($balances as $balance) {
            $result[$balance->currency] = [
                'currency' => $balance->currency,
                'balance' => $balance->balance,
                'locked_balance' => $balance->locked_balance ?? '0.********',
                'available_balance' => $balance->getAvailableBalance(),
                'last_sync' => $balance->web3_synced_at?->toISOString(),
                'web3_data' => [
                    'address' => $balance->getWeb3Address(),
                    'chain_id' => $balance->getWeb3ChainId(),
                    'raw_balance' => $balance->getRawWeb3Balance(),
                    'pending_balance' => $balance->getWeb3PendingBalance(),
                    'exponent' => $balance->getCurrencyExponent(),
                    'is_stable_coin' => $balance->isStableCoin(),
                    'is_cbdc' => $balance->isCbdc(),
                ],
                'sync_status' => $balance->getWeb3SyncStatus(),
            ];
        }

        return $result;
    }

    /**
     * Create a deposit transaction.
     *
     * @param  array  $data
     * @return WalletTransaction
     */
    public function createDeposit(array $data): WalletTransaction
    {
        $this->validateDepositData($data);

        // Get or create account
        $account = WalletAccount::getOrCreateForCustomer($data['customer_id'], $data['currency']);

        return WalletTransaction::createDeposit([
            'customer_id' => $data['customer_id'],
            'account_number' => $account->account_number,
            'method' => $data['method'],
            'currency' => $data['currency'],
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? '0.********',
            'net_amount' => $data['net_amount'] ?? $data['amount'],
            'description' => $data['description'] ?? null,
            'metadata' => $data['metadata'] ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'status' => $data['status'] ?? TransactionStatus::PENDING,
        ]);
    }

    /**
     * Create a withdrawal transaction.
     *
     * @param  array  $data
     * @return WalletTransaction
     */
    public function createWithdrawal(array $data): WalletTransaction
    {
        $this->validateWithdrawalData($data);

        // Get account
        $account = WalletAccount::where('customer_id', $data['customer_id'])
            ->where('currency', $data['currency'])
            ->active()
            ->firstOrFail();

        // Check balance
        $balance = WalletBalance::getCustomerBalance($data['customer_id'], $data['currency']);
        if (! $balance || ! $balance->hasSufficientBalance($data['amount'])) {
            throw new \InvalidArgumentException('Insufficient balance for withdrawal');
        }

        return WalletTransaction::createWithdrawal([
            'customer_id' => $data['customer_id'],
            'account_number' => $account->account_number,
            'method' => $data['method'],
            'currency' => $data['currency'],
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? '0.********',
            'net_amount' => $data['net_amount'] ?? $data['amount'],
            'description' => $data['description'] ?? null,
            'metadata' => $data['metadata'] ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'status' => $data['status'] ?? TransactionStatus::PENDING,
        ]);
    }

    /**
     * Process a completed deposit.
     *
     * @param  WalletTransaction  $transaction
     * @param  array  $data
     * @return bool
     */
    public function processCompletedDeposit(WalletTransaction $transaction, array $data = []): bool
    {
        if ($transaction->type !== TransactionType::DEPOSIT) {
            throw new \InvalidArgumentException('Transaction is not a deposit');
        }

        if ($transaction->isCompleted()) {
            return true; // Already processed
        }

        DB::beginTransaction();

        try {
            // Mark transaction as completed
            $transaction->markAsCompleted($data);

            // Update balance if WEB3 sync is enabled
            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transaction->customer_id, $transaction->currency);
            }

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Process a completed withdrawal.
     *
     * @param  WalletTransaction  $transaction
     * @param  array  $data
     * @return bool
     */
    public function processCompletedWithdrawal(WalletTransaction $transaction, array $data = []): bool
    {
        if ($transaction->type !== TransactionType::WITHDRAWAL) {
            throw new \InvalidArgumentException('Transaction is not a withdrawal');
        }

        if ($transaction->isCompleted()) {
            return true; // Already processed
        }

        DB::beginTransaction();

        try {
            // Mark transaction as completed
            $transaction->markAsCompleted($data);

            // Update balance if WEB3 sync is enabled
            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transaction->customer_id, $transaction->currency);
            }

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get transaction history for a customer.
     *
     * @param  int  $customerId
     * @param  array  $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getTransactionHistory(int $customerId, array $filters = [])
    {
        $query = WalletTransaction::where('customer_id', $customerId)
            ->orderBy('created_at', 'desc');

        // Apply filters
        if (isset($filters['currency'])) {
            $query->forCurrency($filters['currency']);
        }

        if (isset($filters['type'])) {
            $query->ofType($filters['type']);
        }

        if (isset($filters['status'])) {
            $query->withStatus(TransactionStatus::from($filters['status']));
        }

        if (isset($filters['method'])) {
            $query->byMethod($filters['method']);
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get available payment methods for a customer.
     *
     * @param  int  $customerId
     * @param  string  $operation
     * @param  string|null  $currency
     * @return array
     */
    public function getAvailablePaymentMethods(int $customerId, string $operation, ?string $currency = null): array
    {
        $methods = wallet_config('payment_methods', []);
        $available = [];

        foreach ($methods as $methodKey => $methodConfig) {
            if (! $methodConfig['enabled']) {
                continue;
            }

            $canPerformOperation = match ($operation) {
                'deposit' => $methodConfig['can_deposit'],
                'withdraw' => $methodConfig['can_withdraw'],
                default => false,
            };

            if (! $canPerformOperation) {
                continue;
            }

            if ($currency && ! in_array($currency, $methodConfig['supported_currencies'])) {
                continue;
            }

            // TODO: Add customer-specific permission checks here
            // Check if customer has permission to use this method

            $available[$methodKey] = $methodConfig;
        }

        return $available;
    }

    /**
     * Validate deposit data.
     *
     * @param  array  $data
     * @throws \InvalidArgumentException
     */
    protected function validateDepositData(array $data): void
    {
        $required = ['customer_id', 'method', 'currency', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        if (! wallet_is_supported_currency($data['currency'])) {
            throw new \InvalidArgumentException("Unsupported currency: {$data['currency']}");
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw new \InvalidArgumentException('Amount must be greater than zero');
        }

        $methodConfig = wallet_payment_method_config($data['method']);
        if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_deposit']) {
            throw new \InvalidArgumentException("Invalid or disabled deposit method: {$data['method']}");
        }
    }

    /**
     * Validate withdrawal data.
     *
     * @param  array  $data
     * @throws \InvalidArgumentException
     */
    protected function validateWithdrawalData(array $data): void
    {
        $required = ['customer_id', 'method', 'currency', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }

        if (! wallet_is_supported_currency($data['currency'])) {
            throw new \InvalidArgumentException("Unsupported currency: {$data['currency']}");
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw new \InvalidArgumentException('Amount must be greater than zero');
        }

        $methodConfig = wallet_payment_method_config($data['method']);
        if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_withdraw']) {
            throw new \InvalidArgumentException("Invalid or disabled withdrawal method: {$data['method']}");
        }
    }
}
