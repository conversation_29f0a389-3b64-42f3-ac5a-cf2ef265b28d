<?php

namespace Thorne\QontoDeposit\Models;

use App\Services\OAuth\ApiClientAuth10Service;
use App\Traits\HasMeta;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Thorne\QontoDeposit\Enums\Qonto\OperationType;
use Thorne\QontoDeposit\Enums\Qonto\SideType;
use Thorne\QontoDeposit\Http\Controllers\QontoController;
use Thorne\QontoDeposit\Jobs\DepositTransferJob;
use Thorne\WalletDeposit\Enums\TransactionDirection;
use Thorne\WalletDeposit\Enums\TransactionStatus;
use Thorne\WalletDeposit\Enums\TransactionType;
use Thorne\WalletDeposit\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;

class QontoTransaction extends Model
{
    use HasFactory, HasMeta;

    protected $fillable = [
        'customer_id',
        'transaction_id',
        'transaction',
    ];

    protected $casts = [
        'transaction'  => 'array',
    ];

    public function walletTransaction(): HasOne
    {
        return $this->hasOne(WalletTransaction::class, 'source_id', 'transaction_id')
            ->where('source', QontoController::SOURCE);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function isCreditTransfer(): bool
    {
        return ($this->transaction['side'] ?? null)        === SideType::CREDIT->value &&
            ($this->transaction['operation_type'] ?? null) === OperationType::TRANSFER->value;
    }

    public function createWalletTransaction(): void
    {
        $walletTransaction = $this->customer
            ->walletTransactions()
            ->create([
                'source'    => QontoController::SOURCE,
                'source_id' => $this->transaction_id,
                'type'      => TransactionType::DEPOSIT,
                'direction' => TransactionDirection::INBOUND,
                'status'    => TransactionStatus::PENDING,
                'currency'  => $this->transaction['currency'] ?? 'EUR',
                'amount'    => $this->transaction['amount'],
                'raw_data'  => $this->transaction,
            ]);

        $apiService = app(ApiClientAuth10Service::class);
        dispatch(new DepositTransferJob($walletTransaction, $apiService))->onQueue('launchpad');
    }
}
