<?php

namespace App\Jobs;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Bus\Queueable;
// use Illuminate\Support\Facades\Redis;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
// use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Support\Facades\Log;

class SendLaunchToken implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $timeout = 30;

    public $maxExceptions = 1;

    public object $lockToken;

    public $order;

    public $apiServices;

    public function __construct(
        $order
    ) {
        $this->order     = $order;
        $productCurrency = $this->order->items()->first()->product?->sku            ?? 'MIRUM';
        $productChain    = $this->order->items()->first()->product?->chain_id_label ?? 'mirum-testnet';
        $productPlan     = $this->order->items()->first()->product?->product_number ?? 'SEED';

        $this->lockToken = (object) [
            'tenantId'        => 50,
            'userId'          => $this->order->customer_id,
            'chainId'         => $productChain,
            'amount'          => $this->order->total_qty_ordered * pow(10, 8),
            'currency'        => $productCurrency,
            'exponent'        => 8,
            'lockTokenDetail' => (object) [
                'lockPlan' => $productPlan,
            ],
        ];
        $this->apiServices = new ApiClientAuth10Service;
    }

    public function handle()
    {
        try {
            if ($this->order->status === 'completed') {
                return;
            }

            $LockToken = $this->apiServices->baseUrl2('/wallet/launchtoken')
                ->baseMethod('post')
                ->baseClient($this->lockToken);

            if ($LockToken->successful()) {
                $LockToken = $LockToken->json();
            } else {
                throw new Exception('Error SendLaunchToken');
            }

            if (is_array($LockToken)) {
                Log::channel('jobLog')
                    ->info('success', [
                        'data'     => ($this->lockToken),
                        'response' => $LockToken,
                        'endpoint' => $this->apiServices->baseURL,
                        'panelUrl' => 'SendLaunchToken',
                        'userInfo' => $this->order->customer,
                    ]);

                $this->order->forceFill([
                    'status' => 'completed',
                ])->save();

                if ($this->order->payment->method === 'moneytransfer') {
                    if (isset($LockToken['paymentResponse']['txId'])) {
                        $this->order->meta()->createMany([
                            ['meta_key' => 'txId', 'meta_value' => $LockToken['paymentResponse']['txId']],
                        ]);
                    }
                }

                dispatch(new CryptoPaymentCheckTxCustomer($this->order))->onQueue('launchpad');
                dispatch(new SendLaunchTokenCustomer($this->order, $this->lockToken))->onQueue('launchpad');
                dispatch(new CryptoPaymentCheckTxAdmin($this->order))->onQueue('launchpad');
            } else {
                $this->order->meta()->create(['meta_key' => 'txError', 'meta_value' => now()]);

                Log::channel('jobLog')
                    ->error('error', [
                        'data'       => ($this->lockToken),
                        'response'   => $LockToken,
                        'endpoint'   => $this->apiServices->baseURL,
                        'panelUrl'   => 'SendLaunchToken',
                        'userInfo'   => $this->order->customer,
                        'validation' => $this->validatePayload(),
                    ]);
                dispatch(new SendLaunchTokenAdmin($this->order, $this->lockToken, 'SendLaunchToken EndPoint Boş Dönüyor.'))->onQueue('launchpad');
                throw new \Exception('EndPoint Boş Dönüyor.');
            }
        } catch (\Exception $exception) {
            dispatch(new SendLaunchTokenAdmin($this->order, $this->lockToken, 'SendLaunchToken EndPoint Hatası'))->onQueue('launchpad');
            throw new \Exception('EndPoint Hatası : '.$exception->getMessage());
        }
    }

    private function validatePayload(): bool|string
    {
        if ($this->lockToken->tenantId !== 0) {
            return 'tenantId geçersiz';
        }
        if ($this->lockToken->chainId !== 'mirum-testnet') {
            return 'chainId geçersiz';
        }
        if ($this->lockToken->currency !== 'MIRUM') {
            return 'currency geçersiz';
        }
        if ($this->lockToken->lockTokenDetail->lockPlan !== 'SEED') {
            return 'lockPlan geçersiz';
        }

        return true;
    }
}
