<?php

namespace App\Console;

use App\Jobs\CancelExpiredOrder;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Webkul\Sales\Models\Order;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('booking:cron')->dailyAt('3:00');
        $schedule->command('invoice:cron')->dailyAt('3:00');

        $schedule->call(function () {
            $expiredOrders = Order::where('status', 'pending')
                ->whereHas('payment', function ($query) {
                    $query->where('method', 'moneytransfer')
                        ->where('created_at', '<=', now()->subHours(24));
                })
                ->get();

            foreach ($expiredOrders as $order) {
                CancelExpiredOrder::dispatch($order->id)->onQueue('launchpad');
            }
        })->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/../../packages/Webkul/Core/src/Console/Commands');

        require base_path('routes/console.php');
    }
}
