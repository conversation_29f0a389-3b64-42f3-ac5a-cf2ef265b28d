<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        // Check if wallet exception handler should handle this
        if (class_exists('\Thorne\Wallet\Exceptions\WalletExceptionHandler')) {
            $walletHandler = \Thorne\Wallet\Exceptions\WalletExceptionHandler::class;

            if ($walletHandler::shouldHandle($exception, $request)) {
                $response = $walletHandler::handle($exception, $request);

                if ($response instanceof \Illuminate\Http\JsonResponse) {
                    return $response;
                }
            }
        }

        return parent::render($request, $exception);
    }
}
