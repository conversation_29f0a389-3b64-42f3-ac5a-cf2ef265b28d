<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class Google2faRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'one_time_password' => 'required|numeric|min_digits:6|max_digits:6',
            'key'               => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'one_time_password.required'   => 'Password is required and must be 6 number',
            'one_time_password.numeric'    => 'Password is required and must be 6 number',
            'one_time_password.min_digits' => 'Password is required and must be 6 number',
            'one_time_password.max_digits' => 'Password is required and must be 6 number',
            'key.required'                 => 'A message is required',
        ];
    }
}
